import React from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/lib/theme/theme-provider';
import { createDynamicStyles } from '../screens/story-detail-screen.styles';

export function StoryDetailEmpty() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const styles = createDynamicStyles(theme);
  
  return (
    <View style={styles.centered}>
      <Text style={styles.errorText}>
        {t('storyDetail.errors.notFound', 'Story not found.')}
      </Text>
    </View>
  );
}
