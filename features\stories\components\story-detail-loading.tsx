import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useTheme } from '@/lib/theme/theme-provider';
import { createDynamicStyles } from '../screens/story-detail-screen.styles';

export function StoryDetailLoading() {
  const { theme } = useTheme();
  const styles = createDynamicStyles(theme);
  
  return (
    <View style={styles.centered}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
    </View>
  );
}
