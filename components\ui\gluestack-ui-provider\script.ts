export const script = (mode: string) => {
  const documentElement = document.documentElement;

  function getSystemColorMode() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light';
  }

  try {
    const isSystem = mode === 'system';
    const theme = isSystem ? getSystemColorMode() : mode;

    // 清除所有可能的类
    documentElement.classList.remove('dark');
    documentElement.classList.remove('light');

    // 添加正确的类
    documentElement.classList.add(theme);

    // 设置颜色方案
    documentElement.style.colorScheme = theme;

    console.log('Applied theme:', theme);
  } catch (e) {
    console.error(e);
  }
};
