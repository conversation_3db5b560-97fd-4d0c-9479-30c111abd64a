import { Tabs, useRouter } from 'expo-router';
import { Settings } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';
import M3ETabBar from '@/components/ui/m3e-tab-bar';
import { useTheme } from '@/lib/theme/theme-provider';
import { useTranslation } from 'react-i18next';

export default function TabLayout() {
  const { colors, fonts, spacing } = useTheme();
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <Tabs
      tabBar={(props) => <M3ETabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: t('tabs.home'),
        }}
      />
      <Tabs.Screen
        name="create"
        options={{
          title: t('tabs.create'),
        }}
      />
      <Tabs.Screen
        name="stories"
        options={{
          title: t('tabs.stories'),
        }}
      />
      <Tabs.Screen
        name="social"
        options={{
          title: t('tabs.social'),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: t('tabs.profile'),
          headerShown: true,
          headerStyle: {
            backgroundColor: colors.background,
            boxShadow: 'none',
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
          },
          headerTitleStyle: {
            color: colors.text,
            fontFamily: fonts.bold,
          },
          headerRight: () => (
            <TouchableOpacity
              onPress={() => router.push('/(settings)')}
              style={{ marginRight: spacing.md }}
            >
              <Settings size={24} color={colors.text} />
            </TouchableOpacity>
          ),
        }}
      />
    </Tabs>
  );
}
